/**
 * 人事管理系统组件
 *
 * 主要功能：
 * 1. 部门管理 - 显示公司组织架构树形结构，支持部门选择
 * 2. 员工管理 - 员工列表展示、添加、编辑、查看详情
 * 3. 员工搜索 - 支持按姓名实时搜索员工
 * 4. 数据统计 - 显示在职、入职、离职、待入职员工数量统计
 * 5. 分页功能 - 支持员工列表分页显示
 *
 * 技术特点：
 * - 使用 Ant Design 组件库构建UI
 * - 采用 MobX 进行状态管理
 * - 支持表单验证和错误提示
 * - 实现搜索防抖优化性能
 */

import { useState, useEffect, useRef } from "react";
import { observer } from "mobx-react-lite";
import { useLocation } from "react-router-dom";
import {
  Layout,
  Card,
  Button,
  Input,
  Table,
  Modal,
  Form,
  Select,
  DatePicker,
  message,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Tag,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  SearchOutlined,
  UserOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { employeeApi } from "../services/hrService";
import {
  searchDepartment,
  getAllOrganizations,
} from "../services/organizationService";
import dayjs from "dayjs";

const { Content, Sider } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;

// 电话号码验证正则表达式
const PHONE_REGEX = /^1[3-9]\d{9}$/;

/**
 * 人事管理组件 - 管理公司人力资源和员工信息
 * 主要功能：员工列表展示、添加员工、编辑员工、查看员工详情、部门管理
 */
export const HRManagement = observer(() => {
  // 路由信息
  const location = useLocation();

  // 添加调试日志
  console.log("HRManagement: 组件渲染，当前路径:", location.pathname);

  // 如果不在人员管理页面，直接返回null，不渲染任何内容
  if (location.pathname !== "/hr-management/personnel") {
    console.log("HRManagement: 不在人员管理页面，不渲染组件");
    return null;
  }

  // 消息提示
  const [, contextHolder] = message.useMessage();

  // 基础数据状态
  const [departments, setDepartments] = useState([]); // 部门树形数据
  const [loading, setLoading] = useState(true); // 页面加载状态
  const [error, setError] = useState(null); // 错误信息
  const [selectedDepartment, setSelectedDepartment] = useState(null); // 当前选中部门

  // 员工相关状态
  const [employeeList, setEmployeeList] = useState([]); // 员工列表
  const [employeesLoading, setEmployeesLoading] = useState(false); // 员工数据加载状态
  const [selectedEmployees, setSelectedEmployees] = useState([]); // 选中的员工ID列表
  const [searchTerm, setSearchTerm] = useState(""); // 搜索关键词
  const searchTimeoutRef = useRef(null); // 搜索防抖定时器
  const fetchEmployeeTimeoutRef = useRef(null); // 员工数据获取防抖定时器
  const lastFetchParamsRef = useRef(null); // 记录上次获取参数，避免重复请求

  // 弹窗状态
  const [showEmployeeDetailModal, setShowEmployeeDetailModal] = useState(false); // 员工详情弹窗
  const [showEditModal, setShowEditModal] = useState(false); // 编辑员工弹窗
  const [showAddModal, setShowAddModal] = useState(false); // 添加员工弹窗
  const [showDeleteModal, setShowDeleteModal] = useState(false); // 删除确认弹窗

  // 员工数据
  const [selectedEmployee, setSelectedEmployee] = useState(null); // 当前查看的员工
  const [editingEmployee, setEditingEmployee] = useState(null); // 正在编辑的员工
  const [employeeToDelete, setEmployeeToDelete] = useState(null); // 待删除的员工

  // 新员工表单数据
  const [newEmployee, setNewEmployee] = useState({
    name: "",
    position: "",
    organizationId: "",
    status: "在职",
    joinDate: "",
    email: "",
    phone: "",
    skill: "",
    username: "",
    password: "",
  });

  // 员工概览统计数据
  const [overview, setOverview] = useState({
    online: 0, // 在职员工数
    onlinemonth: 0, // 本月入职数
    deonlinemonth: 0, // 本月离职数
    readyonline: 0, // 待入职数
  });

  // 分页配置
  const [pagination, setPagination] = useState({
    current: 0,
    pageSize: 10,
    total: 0,
  });

  // 部门选项数据
  const [departmentOptions, setDepartmentOptions] = useState([]);

  // 表单验证错误信息
  const [fieldErrors, setFieldErrors] = useState({
    name: "",
    organizationId: "",
    position: "",
    joinDate: "",
    phone: "",
    username: "",
    password: "",
    leave: "",
  });

  // 员工状态映射
  const statusMap = {
    在职: 1,
    离职: 0,
    待入职: 2,
  };

  // 状态反向映射，用于显示
  const reverseStatusMap = {
    1: "在职",
    0: "离职",
    2: "待入职",
  };

  /**
   * 获取部门选项列表
   */
  const fetchDepartmentOptions = async () => {
    try {
      const data = await searchDepartment();
      // 确保 data 是数组，如果不是则使用空数组
      setDepartmentOptions(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("获取部门列表错误:", error);
      // 发生错误时设置为空数组
      setDepartmentOptions([]);
    }
  };

  /**
   * 获取组织架构数据
   */
  const fetchOrgData = async () => {
    try {
      setLoading(true);
      const data = await getAllOrganizations();
      const processedData = processOrgData(data);
      setDepartments(processedData);

      setError(null);
      return processedData; // 返回处理后的数据供后续使用
    } catch (err) {
      setError(err.message);
      console.error("获取组织架构数据出错:", err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理组织架构数据，转换为树形结构
   */
  const processOrgData = (orgData) => {
    const processNode = (node) => {
      if (!node) return null;

      return {
        id: node.id,
        name: node.name,
        children: node.children
          ? node.children.map((child) => processNode(child)).filter(Boolean)
          : [],
      };
    };

    return processNode(orgData);
  };

  /**
   * 获取员工列表数据（带防重复调用机制）
   */
  const fetchEmployeeData = async (organizationId) => {
    // 生成请求参数的唯一标识
    const requestKey = `${organizationId}-${pagination.current}-${pagination.pageSize}`;

    // 如果与上次请求参数相同，且正在加载中，则跳过
    if (lastFetchParamsRef.current === requestKey && employeesLoading) {
      console.log("跳过重复的员工数据请求:", requestKey);
      return;
    }

    // 清除之前的定时器
    if (fetchEmployeeTimeoutRef.current) {
      clearTimeout(fetchEmployeeTimeoutRef.current);
    }

    // 使用防抖机制，避免快速连续调用
    fetchEmployeeTimeoutRef.current = setTimeout(async () => {
      try {
        // 记录当前请求参数
        lastFetchParamsRef.current = requestKey;
        setEmployeesLoading(true);

        console.log("获取员工数据:", {
          organizationId,
          page: pagination.current,
          size: pagination.pageSize,
        });

        const data = await employeeApi.getEmployeeList(
          organizationId,
          pagination.current,
          pagination.pageSize
        );

        // 处理员工数据，统一格式
        const processedEmployees = (data.content || [])
          .filter((emp) => emp)
          .map((emp) => ({
            id: emp.id,
            name: emp.name,
            position: emp.position || "-",
            status: reverseStatusMap[emp.online] || "离职",
            joinDate: emp.enter,
            phone: emp.phone,
            email: emp.email,
            number: emp.number,
            enter: emp.enter,
            skill: emp.skill,
            organizationId: emp.organizationId,
            organization: emp.organization,
            online: emp.online,
          }));

        setEmployeeList(processedEmployees);
        setPagination((prev) => ({
          ...prev,
          total: data.totalElements || 0,
        }));
      } catch (err) {
        console.error("获取员工数据错误:", err);
        setEmployeeList([]);
      } finally {
        setEmployeesLoading(false);
        // 清除请求参数记录
        lastFetchParamsRef.current = null;
      }
    }, 100); // 100ms 防抖延迟
  };

  /**
   * 获取员工概览统计数据
   */
  const fetchOverview = async () => {
    try {
      const data = await employeeApi.getEmployeeOverview();
      setOverview(data);
    } catch (err) {
      console.error("获取员工概览数据错误:", err);
    }
  };

  // 添加初始化标志，防止重复初始化
  const [isInitialized, setIsInitialized] = useState(false);
  const isInitializingRef = useRef(false); // 防止并发初始化

  // 初始化数据
  useEffect(() => {
    // 只在当前路由是人员管理页面时才执行初始化
    if (location.pathname !== "/hr-management/personnel") {
      return;
    }

    // 防止重复初始化
    if (isInitialized || isInitializingRef.current) {
      return;
    }

    const initData = async () => {
      console.log("HRManagement: 开始初始化数据");
      isInitializingRef.current = true;
      setIsInitialized(true);

      try {
        const orgData = await fetchOrgData();
        await fetchOverview();

        // 获取组织架构数据后，自动选择第一个部门
        if (orgData) {
          console.log("组织架构数据:", orgData); // 调试信息

          // 递归查找第一个有效的部门ID（包括ID为0的根节点）
          const findFirstDepartment = (node) => {
            if (!node) return null;

            // 如果当前节点有ID（包括0），返回该ID
            if (node.id !== undefined && node.id !== null) {
              return node.id;
            }

            // 如果有子节点，递归查找
            if (node.children && node.children.length > 0) {
              for (const child of node.children) {
                const result = findFirstDepartment(child);
                if (result !== null && result !== undefined) return result;
              }
            }

            return null;
          };

          const firstDepartment = findFirstDepartment(orgData);
          console.log("选择的第一个部门ID:", firstDepartment); // 调试信息
          console.log("组织架构根节点:", orgData.name, "ID:", orgData.id); // 调试信息

          if (firstDepartment !== null && firstDepartment !== undefined) {
            console.log("设置选中部门:", firstDepartment);
            setSelectedDepartment(firstDepartment);
            // 不在这里调用 fetchEmployeeData，让下面的 useEffect 处理
          } else {
            console.log("未找到有效的部门ID");
          }
        }
      } catch (error) {
        console.error("初始化数据失败:", error);
      } finally {
        isInitializingRef.current = false;
      }
    };
    initData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname, isInitialized]); // 依赖路径变化和初始化状态

  // 监听部门选择和分页变化，重新获取数据
  useEffect(() => {
    // 只在当前路由是人员管理页面时才执行
    if (location.pathname !== "/hr-management/personnel") {
      return;
    }

    // 只有在已初始化且有选中部门时才获取员工数据
    if (isInitialized && selectedDepartment !== null) {
      console.log("HRManagement: 部门或分页变化，重新获取员工数据");
      fetchEmployeeData(selectedDepartment);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    selectedDepartment,
    pagination.current,
    location.pathname,
    isInitialized,
  ]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      if (fetchEmployeeTimeoutRef.current) {
        clearTimeout(fetchEmployeeTimeoutRef.current);
      }
    };
  }, []);

  /**
   * 处理搜索输入，支持实时搜索
   */
  const handleSearchInput = async (value) => {
    setSearchTerm(value);

    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 如果搜索词为空，显示当前部门的员工
    if (!value.trim()) {
      // 触发重新获取数据，通过重置分页来触发 useEffect
      setPagination((prev) => ({ ...prev, current: 0 }));
      return;
    }

    // 防抖搜索
    searchTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await employeeApi.searchEmployeesMixed(value);
        const processedResults = (response.data || []).map((emp) => ({
          id: emp.id,
          name: emp.name,
          position: emp.position || "-",
          status: reverseStatusMap[emp.online] || "离职",
          enter: emp.enter || "",
          joinDate: emp.enter,
          phone: emp.phone || "",
          email: emp.email || "",
          number: emp.number || "",
          skill: emp.skill || "",
          organizationId: emp.organizationId,
          organization: emp.organization,
          online: emp.online,
        }));

        setEmployeeList(processedResults);
        setPagination((prev) => ({
          ...prev,
          current: 0,
          total: processedResults.length,
        }));
      } catch (error) {
        console.error("搜索失败:", error);
      }
    }, 300);
  };

  // 错误提示现在使用Ant Design的message API处理

  if (loading) {
    return (
      <Layout style={{ height: "100vh", background: "#f5f5f5" }}>
        {contextHolder}
        <Content style={{ padding: "24px", paddingTop: "80px" }}>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "400px",
            }}
          >
            <Space direction="vertical" align="center">
              <div>加载部门数据中...</div>
            </Space>
          </div>
        </Content>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout style={{ height: "100vh", background: "#f5f5f5" }}>
        {contextHolder}
        <Content style={{ padding: "24px", paddingTop: "80px" }}>
          <Card>
            <div style={{ textAlign: "center", padding: "40px" }}>
              <Text
                type="danger"
                style={{
                  fontSize: "16px",
                  marginBottom: "16px",
                  display: "block",
                }}
              >
                {error}
              </Text>
              <Button type="primary" onClick={() => window.location.reload()}>
                重试
              </Button>
            </div>
          </Card>
        </Content>
      </Layout>
    );
  }

  /**
   * 确认删除员工
   */
  const confirmDelete = async () => {
    if (!employeeToDelete) return;

    try {
      const idsToDelete = employeeToDelete.isBatch
        ? selectedEmployees
        : [employeeToDelete.id];

      await employeeApi.deleteEmployees(idsToDelete);

      if (employeeToDelete.isBatch) {
        setSelectedEmployees([]);
      }

      await fetchOverview();
      // 触发重新获取数据，通过重置分页来触发 useEffect
      setPagination((prev) => ({ ...prev, current: 0 }));
      setShowDeleteModal(false);
      setEmployeeToDelete(null);
    } catch (err) {
      console.error("删除员工错误:", err);
      setError(err.message || "删除员工失败");
    }
  };

  /**
   * 编辑员工信息
   */
  const handleEditEmployee = async (employee) => {
    try {
      await fetchDepartmentOptions();
      const detailData = await employeeApi.getEmployeeDetail(employee.id);

      setEditingEmployee({
        id: employee.id,
        name: detailData.name,
        position: detailData.position || "",
        status: reverseStatusMap[detailData.online] || "在职",
        organizationId: detailData.organizationId,
        enter: detailData.enter || "",
        email: detailData.email || "",
        phone: detailData.phone || "",
        skill: detailData.skill || "",
        online: detailData.online,
        leave: detailData.leave || "",
        number: detailData.number || "",
        joinDate: detailData.enter || "",
      });

      setShowEmployeeDetailModal(false);
      setShowEditModal(true);
    } catch (error) {
      console.error("获取员工详情错误:", error);
      setError("获取员工详情失败");
    }
  };

  /**
   * 关闭编辑弹窗
   */
  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setEditingEmployee(null);
    setFieldErrors({
      name: "",
      organizationId: "",
      position: "",
      joinDate: "",
      phone: "",
      username: "",
      password: "",
      leave: "",
    });
  };

  /**
   * 关闭添加员工弹窗
   */
  const handleCloseAddModal = async () => {
    setFieldErrors({
      name: "",
      organizationId: "",
      position: "",
      joinDate: "",
      phone: "",
      username: "",
      password: "",
      leave: "",
    });

    // 清空表单内容
    setNewEmployee({
      name: "",
      position: "",
      organizationId: "",
      status: "在职",
      joinDate: "",
      email: "",
      phone: "",
      skill: "",
      username: "",
      password: "",
    });

    setShowAddModal(false);
    await fetchOverview(); // 刷新概览数据
  };

  /**
   * 添加新员工
   */
  const handleAddEmployee = async () => {
    // 重置错误信息
    setFieldErrors({
      name: "",
      organizationId: "",
      position: "",
      joinDate: "",
      phone: "",
      username: "",
      password: "",
      leave: "",
    });

    // 表单验证
    const errors = {};
    if (!newEmployee.name) errors.name = "请输入姓名";
    if (!newEmployee.organizationId) errors.organizationId = "请选择部门/岗位";
    if (!newEmployee.position) errors.position = "请输入职位";
    if (!newEmployee.username) errors.username = "请输入用户名";
    if (!newEmployee.password) errors.password = "请输入密码";
    if (!newEmployee.joinDate) errors.joinDate = "请选择入职日期";

    // 验证电话号码格式
    if (newEmployee.phone && !PHONE_REGEX.test(newEmployee.phone)) {
      errors.phone = "请输入正确的11位手机号码";
    }

    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      return;
    }

    try {
      await employeeApi.createEmployee({
        name: newEmployee.name,
        enter: newEmployee.joinDate || "",
        phone: newEmployee.phone || "",
        online: statusMap[newEmployee.status],
        organizationId: newEmployee.organizationId,
        position: newEmployee.position || "",
        skill: newEmployee.skill || "",
        email: newEmployee.email || "",
        username: newEmployee.username,
        password: newEmployee.password,
      });

      await handleCloseAddModal();
      handleDepartmentChange(selectedDepartment || 0);
    } catch (error) {
      console.error("添加员工出错:", error);
    }
  };

  /**
   * 切换部门
   */
  const handleDepartmentChange = (departmentId) => {
    setSelectedDepartment(departmentId);
    setPagination((prev) => ({
      ...prev,
      current: 0,
    }));
    // 移除直接调用 fetchEmployeeData，让 useEffect 处理
  };

  /**
   * 部门树组件 - 递归渲染部门层级结构
   */
  const DepartmentTree = ({ node, selectedDepartment, onSelect }) => {
    const [isExpanded, setIsExpanded] = useState(true);
    const hasChildren = node.children && node.children.length > 0;

    return (
      <div style={{ marginLeft: "8px" }}>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            padding: "8px",
            cursor: "pointer",
            borderRadius: "4px",
            backgroundColor:
              selectedDepartment === node.id ? "#e6f7ff" : "transparent",
          }}
          onClick={() => onSelect(node.id)}
          onMouseEnter={(e) => (e.target.style.backgroundColor = "#f5f5f5")}
          onMouseLeave={(e) =>
            (e.target.style.backgroundColor =
              selectedDepartment === node.id ? "#e6f7ff" : "transparent")
          }
        >
          {hasChildren && (
            <Button
              type="text"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded(!isExpanded);
              }}
              style={{ marginRight: "4px", padding: "0", minWidth: "16px" }}
            >
              {isExpanded ? "▼" : "▶"}
            </Button>
          )}
          <span style={{ overflow: "hidden", textOverflow: "ellipsis" }}>
            {node.name}
          </span>
        </div>
        {hasChildren && isExpanded && (
          <div style={{ marginLeft: "16px", borderLeft: "1px solid #d9d9d9" }}>
            {node.children.map((child) => (
              <DepartmentTree
                key={child.id}
                node={child}
                selectedDepartment={selectedDepartment}
                onSelect={onSelect}
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  /**
   * 显示员工详情
   */
  const handleShowEmployeeDetail = async (employee) => {
    try {
      const [detailData, departmentList] = await Promise.all([
        employeeApi.getEmployeeDetail(employee.id),
        searchDepartment(),
      ]);

      if (detailData) {
        const matchedDepartment = departmentList.find(
          (dept) => dept.id === detailData.organizationId
        );

        setSelectedEmployee({
          ...employee,
          position: detailData.organization?.name || "-",
          organizationDetail: {
            ...detailData.organization,
            departmentName: matchedDepartment?.name || "-",
          },
        });
      }
      setShowEmployeeDetailModal(true);
    } catch (error) {
      console.error("显示员工详情错误:", error);
    }
  };

  const handleSaveEdit = async () => {
    // 重置所有错误
    setFieldErrors({
      name: "",
      organizationId: "",
      position: "",
      joinDate: "",
      phone: "",
      username: "",
      password: "",
      leave: "",
    });

    // 检查必填字段
    const errors = {};
    if (!editingEmployee.name) {
      errors.name = "请输入姓名";
    }
    if (!editingEmployee.organizationId) {
      errors.organizationId = "请选择部门/岗位";
    }
    if (!editingEmployee.position) {
      errors.position = "请输入职位";
    }
    if (!editingEmployee.joinDate) {
      errors.joinDate = "请选择入职日期";
    }
    if (editingEmployee.status === "离职" && !editingEmployee.leave) {
      errors.leave = "请选择离职日期";
    }

    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      return;
    }

    try {
      await employeeApi.updateEmployee(editingEmployee.id, {
        name: editingEmployee.name,
        enter: editingEmployee.enter || "",
        phone: editingEmployee.phone || "",
        online: statusMap[editingEmployee.status],
        organizationId: editingEmployee.organizationId,
        position: editingEmployee.position,
        skill: editingEmployee.skill || "",
        email: editingEmployee.email || "",
        leave:
          editingEmployee.status === "离职" ? editingEmployee.leave || "" : "",
        number: editingEmployee.number || "",
      });

      setShowEditModal(false);
      setEditingEmployee(null);
      // 触发重新获取数据，通过重置分页来触发 useEffect
      setPagination((prev) => ({ ...prev, current: 0 }));
      await fetchOverview();
    } catch (error) {
      console.error("更新员工信息出错:", error);
      setError("更新员工信息失败");
    }
  };

  return (
    <Layout style={{ height: "100vh", background: "#f5f5f5" }}>
      {contextHolder}

      {/* Department List Sidebar */}
      <Sider width={300} style={{ background: "#fff", marginRight: 16 }}>
        <Card
          title="部门列表"
          style={{ height: "100%" }}
          styles={{
            body: {
              height: "calc(100% - 57px)",
              padding: "16px",
              overflowY: "auto",
            },
          }}
        >
          {departments && (
            <DepartmentTree
              node={departments}
              selectedDepartment={selectedDepartment}
              onSelect={handleDepartmentChange}
            />
          )}
        </Card>
      </Sider>

      {/* Main Content */}
      <Content style={{ padding: "24px", paddingTop: "80px" }}>
        <div style={{ marginBottom: 32 }}>
          <Title level={2} style={{ marginBottom: 8 }}>
            人事管理
          </Title>
          <Text type="secondary">管理公司人力资源和员工信息</Text>
        </div>

        {/* 员工概览 */}
        <Row gutter={24} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="在职员工"
                value={overview.online}
                valueStyle={{ color: "#1890ff" }}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="本月入职"
                value={overview.onlinemonth}
                valueStyle={{ color: "#52c41a" }}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="本月离职"
                value={overview.deonlinemonth}
                valueStyle={{ color: "#ff4d4f" }}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="待入职"
                value={overview.readyonline}
                valueStyle={{ color: "#722ed1" }}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
        </Row>

        {/* 员工列表容器 */}
        <Card
          title={
            <Row justify="space-between" align="middle">
              <Col>
                <Space>
                  <Title level={4} style={{ margin: 0 }}>
                    员工列表
                  </Title>
                  {selectedEmployees.length > 0 && (
                    <Text type="secondary">
                      已选择 {selectedEmployees.length} 项
                    </Text>
                  )}
                </Space>
              </Col>
              <Col>
                <Space>
                  <Input
                    style={{ width: 300 }}
                    prefix={<SearchOutlined />}
                    placeholder="搜索员工..."
                    value={searchTerm}
                    onChange={(e) => handleSearchInput(e.target.value)}
                  />
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setShowAddModal(true);
                      fetchDepartmentOptions();
                    }}
                  >
                    添加员工
                  </Button>
                </Space>
              </Col>
            </Row>
          }
          style={{ flex: 1 }}
          styles={{ body: { height: "calc(100% - 57px)", padding: 0 } }}
        >
          <Table
            rowSelection={{
              selectedRowKeys: selectedEmployees,
              onChange: setSelectedEmployees,
              getCheckboxProps: () => ({
                disabled: false,
              }),
            }}
            columns={[
              {
                title: "姓名",
                dataIndex: "name",
                key: "name",
                align: "center",
                render: (text) => <Text strong>{text}</Text>,
              },
              {
                title: "职位",
                dataIndex: "position",
                key: "position",
                align: "center",
              },
              {
                title: "工号",
                dataIndex: "number",
                key: "number",
                align: "center",
                render: (text) => text || "-",
              },
              {
                title: "入职日期",
                dataIndex: "enter",
                key: "enter",
                align: "center",
                render: (text) => text || "-",
              },
              {
                title: "状态",
                dataIndex: "status",
                key: "status",
                align: "center",
                render: (status) => (
                  <Tag
                    color={
                      status === "在职"
                        ? "green"
                        : status === "离职"
                        ? "red"
                        : "orange"
                    }
                  >
                    {status}
                  </Tag>
                ),
              },
              {
                title: "联系电话",
                dataIndex: "phone",
                key: "phone",
                align: "center",
                render: (text) => text || "-",
              },
              {
                title: "技能",
                dataIndex: "skill",
                key: "skill",
                align: "center",
                render: (skill) =>
                  skill ? (
                    <Space wrap>
                      {skill.split(",").map((s, idx) => (
                        <Tag key={idx} color="blue" size="small">
                          {s.trim()}
                        </Tag>
                      ))}
                    </Space>
                  ) : (
                    "-"
                  ),
              },
              {
                title: "操作",
                key: "action",
                align: "center",
                render: (_, record) => (
                  <Space>
                    <Tooltip title="详情">
                      <Button
                        size="small"
                        onClick={() => handleShowEmployeeDetail(record)}
                        style={{ border: "0px" }}
                      >
                        <EyeOutlined />
                      </Button>
                    </Tooltip>

                    <Tooltip title="编辑">
                      <Button
                        size="small"
                        style={{ border: "0px" }}
                        onClick={() => handleEditEmployee(record)}
                        disabled={record.status === "离职"}
                      >
                        <EditOutlined />
                      </Button>
                    </Tooltip>
                  </Space>
                ),
              },
            ]}
            dataSource={employeeList}
            loading={employeesLoading}
            rowKey="id"
            scroll={{
              y: employeeList.length > 6 ? 400 : undefined,
            }}
            pagination={{
              current: pagination.current + 1,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]} 条，共 ${total} 条`,
              pageSizeOptions: ["10", "20", "50", "100"],
              locale: {
                items_per_page: "条/页",
                jump_to: "跳至",
                jump_to_confirm: "确定",
                page: "页",
              },
              onChange: (page, pageSize) => {
                setPagination((prev) => ({
                  ...prev,
                  current: page - 1,
                  pageSize,
                }));
                // 移除直接调用 fetchEmployeeData，让 useEffect 处理
              },
            }}
          />
        </Card>
      </Content>

      {/* Employee Detail Modal */}
      <Modal
        title="员工详情"
        open={showEmployeeDetailModal}
        centered
        onCancel={() => setShowEmployeeDetailModal(false)}
        footer={null}
        width={800}
      >
        {selectedEmployee && (
          <div>
            <Row gutter={[24, 16]} style={{ marginBottom: 24 }}>
              <Col span={12}>
                <Text type="secondary">姓名</Text>
                <div>
                  <Text strong>{selectedEmployee.name}</Text>
                </div>
              </Col>
              <Col span={12}>
                <Text type="secondary">部门/岗位</Text>
                <div>
                  <Text strong>
                    {selectedEmployee.organization?.name || "-"}
                  </Text>
                </div>
              </Col>
              <Col span={12}>
                <Text type="secondary">职位</Text>
                <div>
                  <Text strong>{selectedEmployee.position}</Text>
                </div>
              </Col>
              <Col span={12}>
                <Text type="secondary">状态</Text>
                <div>
                  <Tag
                    color={
                      selectedEmployee.status === "在职"
                        ? "green"
                        : selectedEmployee.status === "离职"
                        ? "red"
                        : "orange"
                    }
                  >
                    {selectedEmployee.status}
                  </Tag>
                </div>
              </Col>
            </Row>

            <Card title="基本信息" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text type="secondary">工号</Text>
                  <div>{selectedEmployee.number || "-"}</div>
                </Col>
                <Col span={12}>
                  <Text type="secondary">入职日期</Text>
                  <div>{selectedEmployee.enter || "-"}</div>
                </Col>
                <Col span={12}>
                  <Text type="secondary">离职日期</Text>
                  <div>{selectedEmployee.endTime || "-"}</div>
                </Col>
                <Col span={12}>
                  <Text type="secondary">联系电话</Text>
                  <div>{selectedEmployee.phone || "-"}</div>
                </Col>
                <Col span={12}>
                  <Text type="secondary">邮箱</Text>
                  <div>{selectedEmployee.email || "-"}</div>
                </Col>
              </Row>
            </Card>

            <Card title="工作信息">
              <div style={{ marginBottom: 16 }}>
                <Text type="secondary">工作职责</Text>
                <div>{selectedEmployee.organization?.duty || "-"}</div>
              </div>
              <div>
                <Text type="secondary">技能标签</Text>
                <div style={{ marginTop: 8 }}>
                  {selectedEmployee.skill ? (
                    <Space wrap>
                      {selectedEmployee.skill.split(",").map((skill, index) => (
                        <Tag key={index} color="blue">
                          {skill.trim()}
                        </Tag>
                      ))}
                    </Space>
                  ) : (
                    <Text type="secondary">-</Text>
                  )}
                </div>
              </div>
            </Card>
          </div>
        )}
      </Modal>

      {/* Add Employee Modal */}
      <Modal
        title="添加新员工"
        open={showAddModal}
        centered
        onCancel={handleCloseAddModal}
        footer={[
          <Button key="cancel" onClick={handleCloseAddModal}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleAddEmployee}>
            添加
          </Button>,
        ]}
        width={800}
      >
        <Form layout="vertical">
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="姓名"
                required
                validateStatus={fieldErrors.name ? "error" : ""}
                help={fieldErrors.name}
              >
                <Input
                  value={newEmployee.name}
                  placeholder="请输入姓名"
                  onChange={(e) =>
                    setNewEmployee({
                      ...newEmployee,
                      name: e.target.value,
                    })
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="部门/岗位"
                required
                validateStatus={fieldErrors.organizationId ? "error" : ""}
                help={fieldErrors.organizationId}
              >
                <Select
                  value={newEmployee.organizationId}
                  placeholder="Select a person"
                  onChange={(value) => {
                    setNewEmployee({
                      ...newEmployee,
                      department: value,
                      organizationId: value,
                    });
                  }}
                >
                  {(departmentOptions || []).map((dept) => (
                    <Option key={dept.id} value={dept.id}>
                      {dept.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="职位"
                required
                validateStatus={fieldErrors.position ? "error" : ""}
                help={fieldErrors.position}
              >
                <Input
                  value={newEmployee.position}
                  placeholder="请输入职位"
                  onChange={(e) =>
                    setNewEmployee({
                      ...newEmployee,
                      position: e.target.value,
                    })
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="用户名"
                required
                validateStatus={fieldErrors.username ? "error" : ""}
                help={fieldErrors.username}
              >
                <Input
                  value={newEmployee.username}
                  placeholder="请输入用户名"
                  onChange={(e) =>
                    setNewEmployee({
                      ...newEmployee,
                      username: e.target.value,
                    })
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="密码"
                required
                validateStatus={fieldErrors.password ? "error" : ""}
                help={fieldErrors.password}
              >
                <Input.Password
                  value={newEmployee.password}
                  placeholder="请输入密码"
                  onChange={(e) =>
                    setNewEmployee({
                      ...newEmployee,
                      password: e.target.value,
                    })
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="状态">
                <Select
                  value={newEmployee.status}
                  onChange={(value) =>
                    setNewEmployee({
                      ...newEmployee,
                      status: value,
                    })
                  }
                >
                  <Option value="在职">在职</Option>
                  <Option value="离职">离职</Option>
                  <Option value="待入职">待入职</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="入职日期"
                required
                validateStatus={fieldErrors.joinDate ? "error" : ""}
                help={fieldErrors.joinDate}
              >
                <DatePicker
                  value={
                    newEmployee.joinDate ? dayjs(newEmployee.joinDate) : null
                  }
                  onChange={(_, dateString) =>
                    setNewEmployee({
                      ...newEmployee,
                      joinDate: dateString,
                    })
                  }
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="邮箱">
                <Input
                  type="email"
                  value={newEmployee.email}
                  placeholder="请输入邮箱"
                  onChange={(e) =>
                    setNewEmployee({
                      ...newEmployee,
                      email: e.target.value,
                    })
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="联系电话"
                validateStatus={fieldErrors.phone ? "error" : ""}
                help={fieldErrors.phone}
              >
                <Input
                  maxLength={11}
                  value={newEmployee.phone}
                  placeholder="请输入11位手机号码"
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, "");
                    setNewEmployee({
                      ...newEmployee,
                      phone: value,
                    });
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="技能标签">
                <Input
                  value={newEmployee.skill}
                  placeholder="请输入技能，多个技能用逗号分隔"
                  onChange={(e) =>
                    setNewEmployee({
                      ...newEmployee,
                      skill: e.target.value,
                    })
                  }
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* Edit Employee Modal */}
      <Modal
        title="编辑员工信息"
        centered
        open={showEditModal}
        onCancel={handleCloseEditModal}
        footer={[
          <Button key="cancel" onClick={handleCloseEditModal}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleSaveEdit}>
            更新
          </Button>,
        ]}
        width={800}
      >
        {editingEmployee && (
          <Form layout="vertical">
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="姓名"
                  required
                  validateStatus={fieldErrors.name ? "error" : ""}
                  help={fieldErrors.name}
                >
                  <Input
                    value={editingEmployee.name}
                    onChange={(e) =>
                      setEditingEmployee({
                        ...editingEmployee,
                        name: e.target.value,
                      })
                    }
                    disabled
                    placeholder="请输入姓名"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="部门/岗位"
                  required
                  validateStatus={fieldErrors.organizationId ? "error" : ""}
                  help={fieldErrors.organizationId}
                >
                  <Select
                    value={editingEmployee.organizationId}
                    onChange={(value) =>
                      setEditingEmployee({
                        ...editingEmployee,
                        organizationId: value,
                      })
                    }
                    placeholder="请选择部门"
                  >
                    {(departmentOptions || []).map((dept) => (
                      <Option key={dept.id} value={dept.id}>
                        {dept.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="职位"
                  required
                  validateStatus={fieldErrors.position ? "error" : ""}
                  help={fieldErrors.position}
                >
                  <Input
                    value={editingEmployee.position}
                    onChange={(e) =>
                      setEditingEmployee({
                        ...editingEmployee,
                        position: e.target.value,
                      })
                    }
                    placeholder="请输入职位"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="状态">
                  <Select
                    value={editingEmployee.status}
                    onChange={(value) =>
                      setEditingEmployee({
                        ...editingEmployee,
                        status: value,
                      })
                    }
                  >
                    <Option value="在职">在职</Option>
                    <Option value="离职">离职</Option>
                    <Option value="待入职">待入职</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="入职日期"
                  required
                  validateStatus={fieldErrors.joinDate ? "error" : ""}
                  help={fieldErrors.joinDate}
                >
                  <DatePicker
                    value={
                      editingEmployee.joinDate
                        ? dayjs(editingEmployee.joinDate)
                        : null
                    }
                    onChange={(_, dateString) =>
                      setEditingEmployee({
                        ...editingEmployee,
                        joinDate: dateString,
                      })
                    }
                    style={{ width: "100%" }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="邮箱">
                  <Input
                    type="email"
                    value={editingEmployee.email || ""}
                    onChange={(e) =>
                      setEditingEmployee({
                        ...editingEmployee,
                        email: e.target.value,
                      })
                    }
                    placeholder="请输入邮箱"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="联系电话">
                  <Input
                    value={editingEmployee.phone || ""}
                    onChange={(e) =>
                      setEditingEmployee({
                        ...editingEmployee,
                        phone: e.target.value,
                      })
                    }
                    placeholder="请输入联系电话"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="技能标签">
                  <Input
                    value={editingEmployee.skill || ""}
                    onChange={(e) =>
                      setEditingEmployee({
                        ...editingEmployee,
                        skill: e.target.value,
                      })
                    }
                    placeholder="请输入技能，多个技能用逗号分隔"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        )}
      </Modal>

      {/* 删除确认弹窗 */}
      <Modal
        title="确认删除"
        open={showDeleteModal}
        onCancel={() => {
          setShowDeleteModal(false);
          setEmployeeToDelete(null);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setShowDeleteModal(false);
              setEmployeeToDelete(null);
            }}
          >
            取消
          </Button>,
          <Button key="delete" type="primary" danger onClick={confirmDelete}>
            确认删除
          </Button>,
        ]}
        width={400}
      >
        <p>
          {employeeToDelete?.isBatch
            ? `确定要删除选中的 ${employeeToDelete.count} 名员工吗？`
            : "确定要删除该员工吗？"}
        </p>
      </Modal>
    </Layout>
  );
});
